using Account.Web.Common.Translate;
using CommonLib;
using log4net;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;

namespace Account.Web
{
    public class Global : HttpApplication
    {
        #region Static Configuration
        public static bool IsSignalRReady = false;

        // Static file extensions that should be skipped from processing
        private static readonly List<string> lstExtFile = new List<string>() { ".js", ".css", ".ttf" };

        // Pages that should show language selection
        private static readonly List<string> lstExpShowLanguagePage = new List<string>() {
            "default.aspx", "detail.aspx", "login.aspx", "ocr.aspx", "status.aspx", "tool.aspx", "user.aspx", "version.aspx",
            "desc.aspx", "privacypolicy.aspx", "useragreement.aspx", "smartrecommendation.aspx" };
        #endregion

        protected void Application_PreRequestHandlerExecute(object sender, EventArgs e)
        {
            if (HttpContext.Current.Handler is Page page)
            {
                page.InitComplete += (s, ee) =>
                {
                    if (page == null || page.Header == null || page.Response == null || page.Response.ContentType != "text/html")
                        return;
                    page.SetExtTitle();
                };
            }
        }

        protected void Application_Start(object sender, EventArgs e)
        {
            lock (ConfigHelper.LockObj)
            {
                if (!ConfigHelper.IsOnInit)
                {
                    ConfigHelper.IsOnInit = true;
                    ConfigHelper.InitThread();
                    try
                    {
                        var file = new FileInfo(AppDomain.CurrentDomain.BaseDirectory + "Log4Net.config");
                        log4net.Config.XmlConfigurator.Configure(file);
                    }
                    catch { }
                    ConfigHelper.InitConfig();

                    MsgProcessHelper.Init();
                    RdsCacheHelper.InitLimiter();
                    DisposeEmailHelper.Init();
                    PayUtil.Init();
                    new LocalWaitCache<string>(ConfigHelper.FilePath.TrimEnd('\\'), new TimeSpan(1, 0, 0, 0), false, false, false);
                    MemoryManager.Init();
                }
            }
        }

        private bool ResponseEnd(string key, string value, BlockReason blockReason, bool isEndResponse, bool isAddToBlack, bool isShort = false, bool isStaticBlack = false)
        {
            return HttpContext.Current.EndResponse(key, value, blockReason, isEndResponse, isAddToBlack, isShort, isStaticBlack);
        }

        private void HandleAppPushRequest()
        {
            int type = BoxUtil.GetInt32FromObject(Request.GetValue("type"));
            string price = Request.GetValue("price");
            string remark = Request.GetValue("remark");
            string t = Request.GetValue("t");
            string sign = Request.GetValue("sign");
            var result = PayUtil.appPush(type, price, remark, t, sign);

            Response.ContentType = "application/json";
            Response.Write(JsonConvert.SerializeObject(result));
            Response.End();
        }

        private void HandleAppHeartRequest()
        {
            string t = Request.GetValue("t");
            string sign = Request.GetValue("sign");
            var result = PayUtil.appHeart(t, sign);

            Response.ContentType = "application/json";
            Response.Write(JsonConvert.SerializeObject(result));
            Response.End();
        }

        private void ProcessPageCaching(string path, Uri cacheUrl, string strPhyPath)
        {
            if (LanguagePathModule.IsBackGroundPage(path))
            {
                return;
            }

            // 检查是否为翻译服务的内部请求，避免递归翻译
            if (Request.QueryString["_translation_source"] == "true")
            {
                LogHelper.Log.Debug($"跳过翻译处理（内部请求）: {path}");
                return;
            }

            Response.Clear();
            Response.ContentType = "text/html";
            Response.ContentEncoding = Encoding.UTF8;

            // 获取正确的路径信息 - 考虑URL重写的情况
            var pathInfo = GetCorrectPathInfo(cacheUrl, path);

            // Requirement 4.2: Get language information determined by LanguagePathModule
            // No longer perform duplicate language detection here
            string currentLang = HttpContext.Current.Items["lang"]?.ToString() ?? LanguageConfiguration.DefaultLanguage;

            // Requirement 4.4: Use determined language for cache processing and translation lookup
            if (!ProcessTranslationCache(pathInfo.AbsolutePath, pathInfo.PhysicalPath, currentLang))
            {
                // Generate SEO tags for pages without cache
                try
                {
                    Response.Write("<!DOCTYPE html>");

                    var canonicalUrl = LanguageService.GenerateSmartCanonicalUrl(pathInfo.AbsolutePath, currentLang, Request.QueryString.ToString());
                    var hreflangTags = LanguageService.GenerateSmartHreflangTags(pathInfo.AbsolutePath, currentLang, Request.QueryString.ToString());

                    Response.Write($"<link rel=\"canonical\" href=\"{canonicalUrl}\" />");
                    Response.Write(hreflangTags);
                }
                catch (Exception ex)
                {
                    LogHelper.Log.Error("SEO标签生成失败", ex);
                }
            }
        }

        /// <summary>
        /// 获取正确的路径信息 - 处理URL重写场景
        /// </summary>
        /// <param name="cacheUrl">缓存URL</param>
        /// <param name="requestPath">请求路径</param>
        /// <returns>包含绝对路径和物理路径的信息</returns>
        private (string AbsolutePath, string PhysicalPath) GetCorrectPathInfo(Uri cacheUrl, string requestPath)
        {
            // 检查是否有重写后的路径信息
            string rewrittenPath = HttpContext.Current.Items["RewrittenPath"]?.ToString();

            if (!string.IsNullOrEmpty(rewrittenPath))
            {
                // 使用重写后的路径
                var rewrittenAbsPath = rewrittenPath.TrimStart('/');
                var rewrittenPhyPath = Server.MapPath("~" + rewrittenPath);
                return (rewrittenAbsPath, rewrittenPhyPath);
            }
            else
            {
                // 使用原始路径
                var strAbsPath = cacheUrl.LocalPath.TrimStart('/');
                var strPhyPath = Server.MapPath(cacheUrl.LocalPath);
                return (strAbsPath, strPhyPath);
            }
        }

        /// <summary>
        /// 验证路径是否存在 - 支持URL重写场景和新URL格式
        /// 实现需求1.1、2.1: 支持默认语言简洁URL和根路径访问的路径验证
        /// </summary>
        /// <param name="physicalPath">物理路径</param>
        /// <param name="requestPath">请求路径</param>
        /// <returns>如果路径有效返回true，否则返回false</returns>
        private bool ValidatePathExists(string physicalPath, string requestPath)
        {
            // 首先检查原始物理路径
            if (UrlService.PathExists(physicalPath))
            {
                return true;
            }

            // 如果原始路径不存在，检查是否为语言路径，需要重写后再检查
            try
            {
                // 处理根路径访问的特殊验证逻辑
                if (requestPath == "/" || string.IsNullOrEmpty(requestPath))
                {
                    // 根路径访问，验证默认页面是否存在
                    string defaultPagePath = Server.MapPath("~/default.aspx");
                    bool defaultPageExists = UrlService.PathExists(defaultPagePath);

                    // 记录根路径验证结果
                    if (HttpContext.Current != null)
                    {
                        HttpContext.Current.Items["RootPathValidation"] = defaultPageExists;
                        HttpContext.Current.Items["DefaultPagePath"] = defaultPagePath;
                    }

                    return defaultPageExists;
                }

                string language, originalLanguagePath, actualPath;
                if (UrlService.TryExtractLanguage("/" + requestPath, out language, out originalLanguagePath, out actualPath))
                {
                    // 这是一个语言路径，检查重写后的路径是否存在
                    string rewrittenPath;
                    if (string.IsNullOrEmpty(actualPath))
                    {
                        // 语言根路径，重写为默认页面
                        rewrittenPath = Server.MapPath("~/default.aspx");
                    }
                    else
                    {
                        // 带具体路径的语言URL，重写为实际路径
                        rewrittenPath = Server.MapPath("~/" + actualPath);
                    }

                    bool pathExists = UrlService.PathExists(rewrittenPath);

                    // 记录语言路径验证结果
                    if (HttpContext.Current != null)
                    {
                        HttpContext.Current.Items["LanguagePathValidation"] = pathExists;
                        HttpContext.Current.Items["LanguagePathRewritten"] = rewrittenPath;
                        HttpContext.Current.Items["OriginalLanguagePath"] = originalLanguagePath;
                    }

                    return pathExists;
                }

                // 检查是否为任何已知语言前缀的路径
                if (!LanguageConfiguration.SupportedLanguages
                    .Any(lang => requestPath.StartsWith($"/{lang}/", StringComparison.OrdinalIgnoreCase)))
                {
                    // 这可能是默认语言的简洁URL，直接验证物理文件存在性
                    string simplePath = Server.MapPath("~" + requestPath);
                    bool simplePathExists = UrlService.PathExists(simplePath);

                    // 记录简洁URL验证结果
                    if (HttpContext.Current != null)
                    {
                        HttpContext.Current.Items["SimpleUrlValidation"] = simplePathExists;
                        HttpContext.Current.Items["SimpleUrlPath"] = simplePath;
                        HttpContext.Current.Items["IsDefaultLanguageSimpleUrl"] = true;
                    }

                    return simplePathExists;
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响正常流程
                LogHelper.Log.Error($"ValidatePathExists error for path: {requestPath}", ex);

                // 记录验证异常信息
                if (HttpContext.Current != null)
                {
                    HttpContext.Current.Items["PathValidationError"] = ex.Message;
                    HttpContext.Current.Items["PathValidationException"] = true;
                }
            }

            return false;
        }

        /// <summary>
        /// Processes translation cache and generates smart SEO tags
        /// Updated to use new smart SEO tag generation methods
        /// </summary>
        private bool ProcessTranslationCache(string strAbsPath, string strPhyPath, string currentLang)
        {
            var isCache = false;
            // Check for cached translated content
            if (!Equals(currentLang, LanguageConfiguration.DefaultLanguage))
            {
                var debugInfo = HttpContext.Current.Items["LanguageDebugInfo"] as LanguageDebugInfo;

                // 记录缓存查找开始
                if (debugInfo != null)
                {
                    debugInfo.CacheInfo.StartCacheLookup(strAbsPath, strPhyPath, currentLang);
                }

                // 智能构造pageKey：只有文章页面才包含queryString
                string pageKey = ConstructPageKey(strAbsPath);

                // 记录缓存查找入口
                if (debugInfo != null)
                {
                    debugInfo.AddWarning($"开始缓存查找 - 页面键: {pageKey}, 物理路径: {strPhyPath}, 语言: {currentLang}");
                }

                // 调用缓存查找（详细的调试信息在 UnifiedPageCache 中处理）
                string cachedContent = UnifiedPageCache.GetTranslation(pageKey, strPhyPath, currentLang);

                if (!string.IsNullOrEmpty(cachedContent))
                {
                    // 记录缓存使用结果
                    if (debugInfo != null)
                    {
                        debugInfo.AddWarning($"缓存命中，内容长度: {cachedContent.Length}，准备输出内容");
                    }

                    // Update rating count if present
                    if (cachedContent.IndexOf("\"ratingCount\":\"") > 0)
                    {
                        cachedContent = cachedContent.Replace(
                            CommonHelper.SubString(cachedContent, "\"ratingCount\":\"", "\""),
                            ((DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 100000000000 * 3).ToString());
                    }

                    try
                    {
                        HttpContext.Current.Items["cache"] = true;
                    }
                    catch (Exception oe)
                    {
                        LogHelper.Log.Error(oe);
                    }

                    isCache = true;
                    Response.Write(cachedContent);

                    // Response.End() 会抛出 ThreadAbortException，这是正常行为
                    if (debugInfo != null)
                    {
                        debugInfo.AddWarning($"缓存内容已输出，准备结束响应");
                    }

                    try
                    {
                        Response.End();
                    }
                    catch (System.Threading.ThreadAbortException)
                    {
                        // ThreadAbortException 是 Response.End() 的正常行为，不需要记录为错误
                        throw; // 重新抛出以保持正常的执行流程
                    }
                }
                else
                {
                    // 记录缓存未命中结果
                    if (debugInfo != null)
                    {
                        debugInfo.AddWarning($"缓存未命中，添加到待翻译队列");
                    }

                    // 添加到待翻译队列时保持原有逻辑
                    PendingTranslationManager.AddPendingPage(strAbsPath, strPhyPath);
                }
            }

            return isCache;
        }

        /// <summary>
        /// 智能构造pageKey：统一处理所有需要queryString的页面
        /// </summary>
        /// <param name="absPath">绝对路径</param>
        /// <returns>页面键</returns>
        private string ConstructPageKey(string absPath)
        {
            // 统一处理：所有配置的页面都包含queryString
            if (PageTypeManager.IsQueryStringPage(absPath))
            {
                string queryString = Request.QueryString.ToString();
                if (!string.IsNullOrEmpty(queryString))
                {
                    return $"{absPath}?{queryString}";
                }
            }

            // 其他页面不包含queryString，保持原有逻辑
            return absPath;
        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {
            // Handle SignalR requests
            if (Request.Url.AbsolutePath.StartsWith("/signalr", StringComparison.OrdinalIgnoreCase))
            {
                if (!IsSignalRReady)
                {
                    Response.StatusCode = 503;
                    CompleteRequest();
                }
                return;
            }

            // Security checks - IP and UID blacklist validation
            if (ConfigHelper.IsBlackIpEnable)
            {
                var ip = Request.GetIPAddress();
                var uid = Request.GetValue("uid");

                if (SecurityService.IsBlackListed(ip, uid))
                {
                    if (ResponseEnd("IP/UID黑名单", ip + "|" + uid, BlockReason.IpBlacklisted, true, false, true))
                        return;
                }

                if (!string.IsNullOrEmpty(ip))
                {
                    try
                    {
                        HttpContext.Current.Items["nowIP"] = ip;
                    }
                    catch (Exception oe)
                    {
                        LogHelper.Log.Error(oe);
                    }
                }
            }

            // Note: Language detection and redirection is now handled by LanguagePathModule
            // This ensures unified language processing and eliminates duplicate logic

            // Process request path and handle special endpoints
            var cacheUrl = CommonTranslate.GetCahcePath(Request.Url);
            var strPhyPath = Server.MapPath(cacheUrl.LocalPath);
            var path = cacheUrl.LocalPath.TrimStart('/').Trim().ToLower();

            if (!string.IsNullOrEmpty(path))
            {
                // Handle special API endpoints
                if (path.Contains("apppush"))
                {
                    HandleAppPushRequest();
                    return;
                }
                else if (path.Contains("appheart"))
                {
                    HandleAppHeartRequest();
                    return;
                }

                // Security validation - URL blacklist check
                if (SecurityService.IsUrlBlackListed(path))
                {
                    if (ResponseEnd("URL黑名单", path.Replace(AppDomain.CurrentDomain.BaseDirectory, ""), BlockReason.UrlBlacklisted, true, true, false, true))
                        return;
                }

                // Skip processing for static files
                if (lstExtFile.Any(p => strPhyPath.EndsWith(p)))
                {
                    return;
                }

                // Validate path exists - 考虑URL重写的情况
                var checkPathResult = ValidatePathExists(strPhyPath, path);
                if (!checkPathResult)
                {
                    if (ResponseEnd("路径不存在", path, BlockReason.ResourceNotFound, true, false))
                        return;
                }

                // Handle page caching and translation for .aspx and .html files
                ProcessPageCaching(path, cacheUrl, strPhyPath);
            }
        }

        protected void Application_EndRequest(object sender, EventArgs e)
        {
            var strPath = Request.Url.AbsolutePath.ToLower();

            // Skip SignalR requests
            if (strPath.StartsWith("/signalr"))
            {
                return;
            }

            // Handle HTML response post-processing
            if (HttpContext.Current.Response.ContentType == "text/html")
            {
                // Note: Language debug info is now handled by LanguagePathModule
                // Old LangDebugInfo processing has been removed to eliminate duplicate logic

                // Add analytics and translation scripts for non-cached pages
                if (HttpContext.Current.Items["cache"] == null)
                {
                    if (!LanguagePathModule.IsBackGroundPage(strPath))
                    {
                        // Add analytics code for non-localhost environments
                        if (!Request.Url.Host.Equals("localhost"))
                            HttpContext.Current.Response.Write(CommonRequest.TongJiCode);

                        // Add translation div for specific pages
                        if (!Equals("/", strPath) && !lstExpShowLanguagePage.Any(p => strPath.Contains(p)))
                            HttpContext.Current.Response.Write("<div id=\"translate\" class=\"ignore\" style=\"display: none\"></div>");

                        HttpContext.Current.Response.Write(CommonRequest.TranslageJs);
                    }
                }
                if (Equals(Request.QueryString["TestLang"], "true"))
                {
                    var debugInfo = HttpContext.Current.Items["LanguageDebugInfo"] as LanguageDebugInfo;
                    Response.Write(debugInfo?.GenerateHtmlReport());
                }
            }
        }

        protected void Application_End(object sender, EventArgs e)
        {
            ConfigHelper.IsExit = true;
            LogManager.GetLogger("Application_End").Error("【POD挂了】 时间:" + ServerTime.LocalTime.ToString("yyyy-MM-dd HH:mm:ss"));
            Application_Error(sender, e);
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            var oe = Server.GetLastError();
            if (oe != null)
            {
                LogManager.GetLogger("Application_Error").Error(" URL:" + Request.Url, oe);
                if (oe.Message.Contains("不存在") || oe is HttpRequestValidationException)
                {
                    ResponseEnd("IIS异常", Request.Url.LocalPath, BlockReason.ServiceException, false, false);
                }
                Server.ClearError();
            }
        }
    }
}